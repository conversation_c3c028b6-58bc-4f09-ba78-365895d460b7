# MarkItDown Web Application

A complete web application for the MarkItDown file conversion tool with React frontend and Python backend.

## Features

- **File Upload Interface**: Upload multiple files simultaneously with drag-and-drop support
- **Format Support**: Supports all MarkItDown formats (PDF, DOCX, PPTX, XLSX, images, HTML, etc.)
- **Batch Processing**: Convert multiple files at once with progress tracking
- **Conversion History**: View and manage past conversions with download links
- **File Sharing**: Generate secure share links for converted files
- **User Management**: Authentication system with admin account
- **Docker Deployment**: Single container deployment with persistent storage

## Supported File Formats

Based on MarkItDown library support:
- **Documents**: PDF, DOCX, PPTX, XLSX, XLS
- **Images**: JPG, PNG, GIF, BMP, TIFF
- **Web**: HTML, XML, RSS
- **Data**: CSV, JSON, EPUB
- **Archives**: ZIP files
- **Email**: Outlook MSG files
- **Audio**: WAV, MP3, M4A (with transcription)
- **Notebooks**: Jupyter notebooks (.ipynb)

## Quick Start with Docker

```bash
# Build and run the application
docker build -t vietprogrammer/markitdown .
docker run -p 8080:8080 -v markitdown_data:/app/data vietprogrammer/markitdown

# Or use docker-compose
docker-compose up -d
```

The web interface will be available at http://localhost:8080

## Default Admin Account

- **Username**: admin
- **Password**: rwz.dpj3RDW3gwz5fny

## Project Structure

```
webapp/
├── backend/           # Python Flask/FastAPI backend
├── frontend/          # React TypeScript frontend
├── docker/           # Docker configuration
├── data/             # Persistent data (SQLite DB, converted files)
├── Dockerfile        # Single container build
├── docker-compose.yml
└── README.md
```

## Development Setup

### Backend
```bash
cd backend
pip install -r requirements.txt
python app.py
```

### Frontend
```bash
cd frontend
npm install
npm start
```

## API Endpoints

- `POST /api/upload` - Upload files for conversion
- `GET /api/files` - List user's files and conversion history
- `POST /api/convert` - Start batch conversion
- `GET /api/download/{file_id}` - Download converted file
- `POST /api/share/{file_id}` - Generate share link
- `GET /api/shared/{share_id}` - Access shared file
- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration

## Environment Variables

- `DATABASE_URL` - SQLite database path (default: /app/data/markitdown.db)
- `UPLOAD_FOLDER` - File upload directory (default: /app/data/uploads)
- `CONVERTED_FOLDER` - Converted files directory (default: /app/data/converted)
- `SECRET_KEY` - Flask secret key for sessions
- `ADMIN_PASSWORD` - Override default admin password

## Volume Mounts

- `/app/data` - Persistent storage for database and files

## License

MIT License - see LICENSE file for details.
