# Production Docker Compose configuration with Nginx
version: '3.8'

services:
  markitdown:
    build: 
      context: ..
      dockerfile: Dockerfile
    image: vietprogrammer/markitdown:latest
    container_name: markitdown-webapp
    volumes:
      - markitdown_data:/app/data
    environment:
      - DATABASE_URL=sqlite:///data/markitdown.db
      - UPLOAD_FOLDER=data/uploads
      - CONVERTED_FOLDER=data/converted
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret-key-change-in-production}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-rwz.dpj3RDW3gwz5fny}
      - FLASK_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/supported-formats"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - markitdown-network

  nginx:
    image: nginx:alpine
    container_name: markitdown-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - markitdown_data:/app/data:ro
      # Uncomment for SSL certificates
      # - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - markitdown
    restart: unless-stopped
    networks:
      - markitdown-network

volumes:
  markitdown_data:
    driver: local

networks:
  markitdown-network:
    driver: bridge
