# Multi-stage build for MarkItDown Web Application
FROM node:18-alpine AS frontend-builder

# Set working directory for frontend build
WORKDIR /app/frontend

# Copy frontend package files
COPY frontend/package*.json ./

# Install frontend dependencies
RUN npm ci --only=production

# Copy frontend source code
COPY frontend/ ./

# Build the React application
RUN npm run build

# Python backend stage
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_ENV=production

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create data directory for persistent storage
RUN mkdir -p /app/data/uploads /app/data/converted

# Copy backend requirements and install Python dependencies
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend source code
COPY backend/ ./

# Copy built frontend files to backend static directory
COPY --from=frontend-builder /app/frontend/build ./static

# Create a simple server script to serve both frontend and backend
RUN cat > server.py << 'EOF'
import os
from flask import Flask, send_from_directory, send_file
from app import create_app

# Create the Flask app
app = create_app('production')

# Serve React app static files
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve_react_app(path):
    if path != "" and os.path.exists(os.path.join(app.static_folder, path)):
        return send_from_directory(app.static_folder, path)
    else:
        return send_file(os.path.join(app.static_folder, 'index.html'))

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=False)
EOF

# Create startup script
RUN cat > start.sh << 'EOF'
#!/bin/bash

# Initialize database if it doesn't exist
if [ ! -f /app/data/markitdown.db ]; then
    echo "Initializing database..."
    python -c "
from app import create_app
from models import db, create_default_admin
app = create_app('production')
with app.app_context():
    db.create_all()
    create_default_admin(app)
print('Database initialized successfully!')
"
fi

# Start the application
echo "Starting MarkItDown Web Application..."
python server.py
EOF

RUN chmod +x start.sh

# Set proper permissions for data directory
RUN chown -R 1000:1000 /app/data

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/supported-formats || exit 1

# Set user for security
USER 1000:1000

# Start the application
CMD ["./start.sh"]
