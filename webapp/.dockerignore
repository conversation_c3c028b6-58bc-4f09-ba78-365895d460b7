# Node modules
frontend/node_modules
frontend/build
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Python
backend/__pycache__
backend/*.pyc
backend/*.pyo
backend/*.pyd
backend/.Python
backend/env
backend/venv
backend/.venv
backend/pip-log.txt
backend/pip-delete-this-directory.txt
backend/.tox
backend/.coverage
backend/.coverage.*
backend/.cache
backend/nosetests.xml
backend/coverage.xml
backend/*.cover
backend/*.log
backend/.git
backend/.mypy_cache
backend/.pytest_cache
backend/.hypothesis

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Data directories (will be mounted as volumes)
data/
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Git
.git
.gitignore

# Documentation
README.md
docs/

# Docker
Dockerfile
docker-compose.yml
.dockerignore
