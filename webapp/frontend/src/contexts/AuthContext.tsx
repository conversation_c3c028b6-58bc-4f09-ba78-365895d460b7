import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { apiService } from '../services/apiService';

interface User {
  id: number;
  username: string;
  email?: string;
  is_admin: boolean;
  created_at: string;
  last_login?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  register: (username: string, password: string, email?: string) => Promise<boolean>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token) {
      apiService.setAuthToken(token);
      checkCurrentUser();
    } else {
      setLoading(false);
    }
  }, []);

  const checkCurrentUser = async () => {
    try {
      const response = await apiService.getCurrentUser();
      setUser(response.user);
    } catch (error) {
      localStorage.removeItem('access_token');
      apiService.setAuthToken(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const response = await apiService.login(username, password);
      localStorage.setItem('access_token', response.access_token);
      apiService.setAuthToken(response.access_token);
      setUser(response.user);
      message.success('Login successful!');
      return true;
    } catch (error: any) {
      message.error(error.response?.data?.error || 'Login failed');
      return false;
    }
  };

  const register = async (username: string, password: string, email?: string): Promise<boolean> => {
    try {
      const response = await apiService.register(username, password, email);
      localStorage.setItem('access_token', response.access_token);
      apiService.setAuthToken(response.access_token);
      setUser(response.user);
      message.success('Registration successful!');
      return true;
    } catch (error: any) {
      message.error(error.response?.data?.error || 'Registration failed');
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    apiService.setAuthToken(null);
    setUser(null);
    message.success('Logged out successfully');
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
