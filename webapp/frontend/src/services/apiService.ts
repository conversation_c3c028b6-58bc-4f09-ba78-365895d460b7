import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface User {
  id: number;
  username: string;
  email?: string;
  is_admin: boolean;
  created_at: string;
  last_login?: string;
}

export interface ConversionFile {
  id: number;
  file_id: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  file_extension: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  converted_filename?: string;
  converted_size?: number;
  conversion_started_at?: string;
  conversion_completed_at?: string;
  error_message?: string;
  created_at: string;
  has_share_links: boolean;
}

export interface ShareLink {
  id: number;
  share_id: string;
  file_id: number;
  expires_at?: string;
  access_count: number;
  max_access_count?: number;
  is_active: boolean;
  created_at: string;
  last_accessed_at?: string;
  is_valid: boolean;
}

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: '/api',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token: string | null) {
    if (token) {
      this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.api.defaults.headers.common['Authorization'];
    }
  }

  // Auth endpoints
  async login(username: string, password: string): Promise<{ access_token: string; user: User }> {
    const response = await this.api.post('/auth/login', { username, password });
    return response.data;
  }

  async register(username: string, password: string, email?: string): Promise<{ access_token: string; user: User }> {
    const response = await this.api.post('/auth/register', { username, password, email });
    return response.data;
  }

  async getCurrentUser(): Promise<{ user: User }> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // File endpoints
  async uploadFiles(files: FileList): Promise<{ uploaded_files: ConversionFile[]; errors: any[] }> {
    const formData = new FormData();
    Array.from(files).forEach((file) => {
      formData.append('files', file);
    });

    const response = await this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getUserFiles(page = 1, perPage = 20, status?: string): Promise<{
    files: ConversionFile[];
    total: number;
    pages: number;
    current_page: number;
  }> {
    const params: any = { page, per_page: perPage };
    if (status) params.status = status;

    const response = await this.api.get('/files', { params });
    return response.data;
  }

  async convertFiles(fileIds: string[]): Promise<{ message: string }> {
    const response = await this.api.post('/convert', { file_ids: fileIds });
    return response.data;
  }

  async downloadFile(fileId: string): Promise<Blob> {
    const response = await this.api.get(`/download/${fileId}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async deleteFile(fileId: string): Promise<{ message: string }> {
    const response = await this.api.delete(`/files/${fileId}`);
    return response.data;
  }

  // Share link endpoints
  async createShareLink(fileId: string, expiresInDays?: number, maxAccess?: number): Promise<{
    share_link: ShareLink;
    share_url: string;
  }> {
    const response = await this.api.post(`/share/${fileId}`, {
      expires_in_days: expiresInDays,
      max_access: maxAccess,
    });
    return response.data;
  }

  async getUserShareLinks(): Promise<{ share_links: ShareLink[] }> {
    const response = await this.api.get('/share-links');
    return response.data;
  }

  async deactivateShareLink(shareId: string): Promise<{ message: string }> {
    const response = await this.api.post(`/share/${shareId}/deactivate`);
    return response.data;
  }

  // Utility endpoints
  async getSupportedFormats(): Promise<{ formats: Record<string, string> }> {
    const response = await this.api.get('/supported-formats');
    return response.data;
  }

  // Helper method to get share URL
  getShareUrl(shareId: string): string {
    return `${window.location.origin}/api/shared/${shareId}`;
  }
}

export const apiService = new ApiService();
