.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom styles for the application */
.ant-layout-sider {
  background: #fff !important;
}

.ant-menu {
  border-right: none !important;
}

.ant-upload-drag {
  border: 2px dashed #d9d9d9 !important;
  border-radius: 8px !important;
}

.ant-upload-drag:hover {
  border-color: #1890ff !important;
}

.ant-upload-drag.ant-upload-drag-hover {
  border-color: #1890ff !important;
}

.ant-card {
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  font-weight: 600 !important;
}

.ant-btn-primary {
  border-radius: 6px !important;
}

.ant-input {
  border-radius: 6px !important;
}

.ant-select-selector {
  border-radius: 6px !important;
}

/* Loading spinner */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* File upload area */
.upload-area {
  padding: 40px 20px;
  text-align: center;
}

.upload-area .ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

/* Status indicators */
.status-pending {
  color: #fa8c16;
}

.status-processing {
  color: #1890ff;
}

.status-completed {
  color: #52c41a;
}

.status-failed {
  color: #ff4d4f;
}

/* Responsive design */
@media (max-width: 768px) {
  .ant-layout-sider {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
  }
}

@media (max-width: 576px) {
  .ant-layout-content {
    margin: 16px !important;
    padding: 16px !important;
  }
  
  .ant-card {
    margin: 0 !important;
  }
}
