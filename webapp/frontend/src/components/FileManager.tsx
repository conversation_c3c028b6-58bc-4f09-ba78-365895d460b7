import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  message,
  Popconfirm,
  Select,
  Input,
  Card,
  Typography,
  Progress,
  Tooltip,
  Modal,
} from 'antd';
import {
  DownloadOutlined,
  DeleteOutlined,
  ShareAltOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { apiService, ConversionFile } from '../services/apiService';
import ShareLinkModal from './ShareLinkModal';

const { Title } = Typography;
const { Option } = Select;

const FileManager: React.FC = () => {
  const [files, setFiles] = useState<ConversionFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    status: '',
    search: '',
  });
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState<ConversionFile | null>(null);

  useEffect(() => {
    loadFiles();
  }, [pagination.current, pagination.pageSize, filters]);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const response = await apiService.getUserFiles(
        pagination.current,
        pagination.pageSize,
        filters.status || undefined
      );
      
      let filteredFiles = response.files;
      if (filters.search) {
        filteredFiles = response.files.filter(file =>
          file.original_filename.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      setFiles(filteredFiles);
      setPagination(prev => ({
        ...prev,
        total: response.total,
      }));
    } catch (error: any) {
      message.error(`Failed to load files: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (file: ConversionFile) => {
    try {
      const blob = await apiService.downloadFile(file.file_id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.converted_filename || `${file.original_filename}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('File downloaded successfully!');
    } catch (error: any) {
      message.error(`Download failed: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleDelete = async (fileId: string) => {
    try {
      await apiService.deleteFile(fileId);
      message.success('File deleted successfully!');
      loadFiles();
    } catch (error: any) {
      message.error(`Delete failed: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleShare = (file: ConversionFile) => {
    setSelectedFile(file);
    setShareModalVisible(true);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'processing': return 'blue';
      case 'failed': return 'red';
      case 'pending': return 'orange';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'File Name',
      dataIndex: 'original_filename',
      key: 'original_filename',
      ellipsis: true,
      render: (text: string, record: ConversionFile) => (
        <div>
          <div>{text}</div>
          <small style={{ color: '#666' }}>
            {formatFileSize(record.file_size)} • {record.file_extension}
          </small>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: ConversionFile) => (
        <div>
          <Tag color={getStatusColor(status)}>{status.toUpperCase()}</Tag>
          {status === 'processing' && (
            <Progress percent={50} size="small" showInfo={false} />
          )}
          {status === 'failed' && record.error_message && (
            <Tooltip title={record.error_message}>
              <Button type="link" size="small">View Error</Button>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: 'Converted',
      key: 'converted',
      width: 150,
      render: (record: ConversionFile) => (
        <div>
          {record.converted_filename && (
            <>
              <div>{record.converted_filename}</div>
              <small style={{ color: '#666' }}>
                {formatFileSize(record.converted_size || 0)}
              </small>
            </>
          )}
        </div>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (record: ConversionFile) => (
        <Space>
          {record.status === 'completed' && (
            <>
              <Button
                type="primary"
                size="small"
                icon={<DownloadOutlined />}
                onClick={() => handleDownload(record)}
              >
                Download
              </Button>
              <Button
                size="small"
                icon={<ShareAltOutlined />}
                onClick={() => handleShare(record)}
              >
                Share
              </Button>
            </>
          )}
          <Popconfirm
            title="Are you sure you want to delete this file?"
            onConfirm={() => handleDelete(record.file_id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={4} style={{ margin: 0 }}>My Files</Title>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadFiles}
            loading={loading}
          >
            Refresh
          </Button>
        </div>

        <div style={{ marginBottom: 16, display: 'flex', gap: 16 }}>
          <Input
            placeholder="Search files..."
            prefix={<SearchOutlined />}
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            style={{ width: 300 }}
          />
          <Select
            placeholder="Filter by status"
            value={filters.status}
            onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="pending">Pending</Option>
            <Option value="processing">Processing</Option>
            <Option value="completed">Completed</Option>
            <Option value="failed">Failed</Option>
          </Select>
        </div>

        <Table
          columns={columns}
          dataSource={files}
          rowKey="file_id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} files`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 20,
              }));
            },
          }}
        />
      </Card>

      <ShareLinkModal
        visible={shareModalVisible}
        file={selectedFile}
        onClose={() => {
          setShareModalVisible(false);
          setSelectedFile(null);
        }}
        onSuccess={() => {
          loadFiles();
        }}
      />
    </div>
  );
};

export default FileManager;
