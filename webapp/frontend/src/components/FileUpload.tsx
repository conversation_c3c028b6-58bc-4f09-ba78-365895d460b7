import React, { useState, useEffect } from 'react';
import {
  Upload,
  Button,
  Card,
  List,
  Progress,
  message,
  Typography,
  Space,
  Tag,
  Alert,
  Divider,
} from 'antd';
import {
  InboxOutlined,
  UploadOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { apiService, ConversionFile } from '../services/apiService';

const { Dragger } = Upload;
const { Title, Text } = Typography;

interface UploadedFile extends ConversionFile {
  uploading?: boolean;
  uploadProgress?: number;
}

const FileUpload: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [converting, setConverting] = useState(false);
  const [supportedFormats, setSupportedFormats] = useState<Record<string, string>>({});

  useEffect(() => {
    loadSupportedFormats();
  }, []);

  const loadSupportedFormats = async () => {
    try {
      const response = await apiService.getSupportedFormats();
      setSupportedFormats(response.formats);
    } catch (error) {
      console.error('Failed to load supported formats:', error);
    }
  };

  const handleUpload = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;
    
    // Check file format
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!supportedFormats[fileExtension]) {
      message.error(`Unsupported file format: ${fileExtension}`);
      onError(new Error('Unsupported format'));
      return;
    }

    try {
      setUploading(true);
      
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        onProgress({ percent: Math.random() * 30 + 20 });
      }, 200);

      const fileList = new DataTransfer();
      fileList.items.add(file);
      
      const response = await apiService.uploadFiles(fileList.files);
      
      clearInterval(progressInterval);
      onProgress({ percent: 100 });

      if (response.uploaded_files.length > 0) {
        const uploadedFile = response.uploaded_files[0];
        setUploadedFiles(prev => [...prev, uploadedFile]);
        message.success(`${file.name} uploaded successfully!`);
        onSuccess(uploadedFile);
      }

      if (response.errors.length > 0) {
        response.errors.forEach(error => {
          message.error(`${error.filename}: ${error.error}`);
        });
      }
    } catch (error: any) {
      message.error(`Upload failed: ${error.response?.data?.error || error.message}`);
      onError(error);
    } finally {
      setUploading(false);
    }
  };

  const handleBatchUpload = async (fileList: FileList) => {
    setUploading(true);
    
    try {
      const response = await apiService.uploadFiles(fileList);
      
      if (response.uploaded_files.length > 0) {
        setUploadedFiles(prev => [...prev, ...response.uploaded_files]);
        message.success(`${response.uploaded_files.length} files uploaded successfully!`);
      }

      if (response.errors.length > 0) {
        response.errors.forEach(error => {
          message.error(`${error.filename}: ${error.error}`);
        });
      }
    } catch (error: any) {
      message.error(`Upload failed: ${error.response?.data?.error || error.message}`);
    } finally {
      setUploading(false);
    }
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.file_id !== fileId));
  };

  const startConversion = async () => {
    const fileIds = uploadedFiles.map(f => f.file_id);
    if (fileIds.length === 0) {
      message.warning('No files to convert');
      return;
    }

    setConverting(true);
    try {
      await apiService.convertFiles(fileIds);
      message.success('Conversion started! Check "My Files" to monitor progress.');
      setUploadedFiles([]);
    } catch (error: any) {
      message.error(`Conversion failed: ${error.response?.data?.error || error.message}`);
    } finally {
      setConverting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getSupportedExtensions = () => {
    return Object.keys(supportedFormats).join(', ');
  };

  return (
    <div>
      <Card>
        <Title level={4}>Upload Files for Conversion</Title>
        <Text type="secondary">
          Upload your files to convert them to Markdown format. Multiple files can be uploaded and converted simultaneously.
        </Text>

        <Divider />

        <Dragger
          name="files"
          multiple
          customRequest={handleUpload}
          showUploadList={false}
          disabled={uploading}
          style={{ marginBottom: 24 }}
          onChange={(info) => {
            if (info.fileList.length > 1) {
              // Handle multiple file drop
              const files = info.fileList.map(f => f.originFileObj).filter(Boolean);
              if (files.length > 0) {
                const fileList = new DataTransfer();
                files.forEach(file => fileList.items.add(file as File));
                handleBatchUpload(fileList.files);
              }
            }
          }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">Click or drag files to this area to upload</p>
          <p className="ant-upload-hint">
            Support for single or bulk upload. Select multiple files to upload them all at once.
          </p>
        </Dragger>

        <Alert
          message="Supported Formats"
          description={`${getSupportedExtensions()}`}
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {uploadedFiles.length > 0 && (
          <>
            <Title level={5}>Uploaded Files ({uploadedFiles.length})</Title>
            <List
              dataSource={uploadedFiles}
              renderItem={(file) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeFile(file.file_id)}
                    >
                      Remove
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    title={file.original_filename}
                    description={
                      <Space>
                        <Text type="secondary">{formatFileSize(file.file_size)}</Text>
                        <Tag color="blue">{file.file_extension}</Tag>
                        <Tag color="green">{file.status}</Tag>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />

            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Button
                type="primary"
                size="large"
                icon={<PlayCircleOutlined />}
                loading={converting}
                onClick={startConversion}
              >
                Convert All Files
              </Button>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default FileUpload;
