import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  message,
  Popconfirm,
  Card,
  Typography,
  Input,
  Tooltip,
} from 'antd';
import {
  CopyOutlined,
  DeleteOutlined,
  ReloadOutlined,
  LinkOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { apiService, ShareLink } from '../services/apiService';

const { Title, Text } = Typography;

const ShareManager: React.FC = () => {
  const [shareLinks, setShareLinks] = useState<ShareLink[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadShareLinks();
  }, []);

  const loadShareLinks = async () => {
    setLoading(true);
    try {
      const response = await apiService.getUserShareLinks();
      setShareLinks(response.share_links);
    } catch (error: any) {
      message.error(`Failed to load share links: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async (shareId: string) => {
    const shareUrl = apiService.getShareUrl(shareId);
    try {
      await navigator.clipboard.writeText(shareUrl);
      message.success('Share link copied to clipboard!');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('Share link copied to clipboard!');
    }
  };

  const handleDeactivate = async (shareId: string) => {
    try {
      await apiService.deactivateShareLink(shareId);
      message.success('Share link deactivated successfully!');
      loadShareLinks();
    } catch (error: any) {
      message.error(`Failed to deactivate share link: ${error.response?.data?.error || error.message}`);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const isExpired = (shareLink: ShareLink) => {
    if (!shareLink.expires_at) return false;
    return new Date(shareLink.expires_at) < new Date();
  };

  const isAccessLimitReached = (shareLink: ShareLink) => {
    if (!shareLink.max_access_count) return false;
    return shareLink.access_count >= shareLink.max_access_count;
  };

  const getStatusTag = (shareLink: ShareLink) => {
    if (!shareLink.is_active) {
      return <Tag color="red">Deactivated</Tag>;
    }
    if (isExpired(shareLink)) {
      return <Tag color="orange">Expired</Tag>;
    }
    if (isAccessLimitReached(shareLink)) {
      return <Tag color="orange">Access Limit Reached</Tag>;
    }
    return <Tag color="green">Active</Tag>;
  };

  const columns = [
    {
      title: 'Share Link',
      key: 'share_link',
      render: (record: ShareLink) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <LinkOutlined />
            <Text code style={{ fontSize: '12px' }}>
              {record.share_id.substring(0, 8)}...
            </Text>
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(record.share_id)}
            />
          </div>
          <div style={{ marginTop: 4 }}>
            {getStatusTag(record)}
          </div>
        </div>
      ),
    },
    {
      title: 'File',
      dataIndex: 'file_id',
      key: 'file_id',
      render: (fileId: number) => (
        <Text>File ID: {fileId}</Text>
      ),
    },
    {
      title: 'Access Count',
      key: 'access',
      render: (record: ShareLink) => (
        <div>
          <Text>{record.access_count}</Text>
          {record.max_access_count && (
            <Text type="secondary"> / {record.max_access_count}</Text>
          )}
          {record.last_accessed_at && (
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Last: {formatDate(record.last_accessed_at)}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (expiresAt: string | null, record: ShareLink) => (
        <div>
          {expiresAt ? (
            <div>
              <Text>{formatDate(expiresAt)}</Text>
              {isExpired(record) && (
                <div>
                  <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                  <Text type="danger" style={{ fontSize: '12px', marginLeft: 4 }}>
                    Expired
                  </Text>
                </div>
              )}
            </div>
          ) : (
            <Text type="secondary">Never</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: ShareLink) => (
        <Space>
          <Tooltip title="Copy share link">
            <Button
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(record.share_id)}
            />
          </Tooltip>
          {record.is_active && (
            <Popconfirm
              title="Are you sure you want to deactivate this share link?"
              onConfirm={() => handleDeactivate(record.share_id)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              >
                Deactivate
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={4} style={{ margin: 0 }}>Share Links</Title>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadShareLinks}
            loading={loading}
          >
            Refresh
          </Button>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            Manage your file share links. Share links allow others to download your converted files without needing an account.
          </Text>
        </div>

        <Table
          columns={columns}
          dataSource={shareLinks}
          rowKey="share_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} share links`,
          }}
          locale={{
            emptyText: 'No share links created yet. Create share links from the "My Files" section.',
          }}
        />
      </Card>
    </div>
  );
};

export default ShareManager;
