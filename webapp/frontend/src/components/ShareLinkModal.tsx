import React, { useState } from 'react';
import {
  Modal,
  Form,
  InputNumber,
  Button,
  message,
  Space,
  Typography,
  Input,
  Divider,
} from 'antd';
import { CopyOutlined, LinkOutlined } from '@ant-design/icons';
import { apiService, ConversionFile } from '../services/apiService';

const { Text, Title } = Typography;

interface ShareLinkModalProps {
  visible: boolean;
  file: ConversionFile | null;
  onClose: () => void;
  onSuccess: () => void;
}

const ShareLinkModal: React.FC<ShareLinkModalProps> = ({
  visible,
  file,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [shareUrl, setShareUrl] = useState<string>('');
  const [created, setCreated] = useState(false);

  const handleCreate = async (values: { expiresInDays?: number; maxAccess?: number }) => {
    if (!file) return;

    setLoading(true);
    try {
      const response = await apiService.createShareLink(
        file.file_id,
        values.expiresInDays,
        values.maxAccess
      );
      
      const fullUrl = apiService.getShareUrl(response.share_link.share_id);
      setShareUrl(fullUrl);
      setCreated(true);
      message.success('Share link created successfully!');
      onSuccess();
    } catch (error: any) {
      message.error(`Failed to create share link: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      message.success('Share link copied to clipboard!');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('Share link copied to clipboard!');
    }
  };

  const handleClose = () => {
    setCreated(false);
    setShareUrl('');
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title={
        <Space>
          <LinkOutlined />
          Create Share Link
        </Space>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
    >
      {file && (
        <div>
          <div style={{ marginBottom: 24 }}>
            <Text strong>File: </Text>
            <Text>{file.original_filename}</Text>
            <br />
            <Text strong>Converted: </Text>
            <Text>{file.converted_filename}</Text>
          </div>

          {!created ? (
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreate}
              initialValues={{
                expiresInDays: 7,
                maxAccess: undefined,
              }}
            >
              <Form.Item
                name="expiresInDays"
                label="Expires in (days)"
                help="Leave empty for no expiration"
              >
                <InputNumber
                  min={1}
                  max={365}
                  placeholder="7"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name="maxAccess"
                label="Maximum access count"
                help="Leave empty for unlimited access"
              >
                <InputNumber
                  min={1}
                  max={1000}
                  placeholder="Unlimited"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                  >
                    Create Share Link
                  </Button>
                  <Button onClick={handleClose}>
                    Cancel
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          ) : (
            <div>
              <Title level={5}>Share Link Created!</Title>
              <Text type="secondary">
                Anyone with this link can download the converted file:
              </Text>
              
              <div style={{ marginTop: 16, marginBottom: 16 }}>
                <Input.Group compact>
                  <Input
                    value={shareUrl}
                    readOnly
                    style={{ width: 'calc(100% - 100px)' }}
                  />
                  <Button
                    type="primary"
                    icon={<CopyOutlined />}
                    onClick={handleCopy}
                    style={{ width: 100 }}
                  >
                    Copy
                  </Button>
                </Input.Group>
              </div>

              <Divider />

              <Space>
                <Button type="primary" onClick={handleClose}>
                  Done
                </Button>
                <Button
                  onClick={() => {
                    setCreated(false);
                    setShareUrl('');
                  }}
                >
                  Create Another
                </Button>
              </Space>
            </div>
          )}
        </div>
      )}
    </Modal>
  );
};

export default ShareLinkModal;
