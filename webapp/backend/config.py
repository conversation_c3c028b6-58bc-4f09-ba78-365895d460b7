import os
from datetime import timedelta

class Config:
    # Basic Flask config
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database config
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///data/markitdown.db'
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # File upload config
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'data/uploads'
    CONVERTED_FOLDER = os.environ.get('CONVERTED_FOLDER') or 'data/converted'
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    
    # JWT config
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or SECRET_KEY
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # Admin config
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'rwz.dpj3RDW3gwz5fny'
    
    # CORS config
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*').split(',')
    
    # MarkItDown supported formats
    SUPPORTED_EXTENSIONS = {
        # Documents
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel',
        
        # Images
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.tiff': 'image/tiff',
        '.tif': 'image/tiff',
        
        # Web formats
        '.html': 'text/html',
        '.htm': 'text/html',
        '.xml': 'text/xml',
        
        # Data formats
        '.csv': 'text/csv',
        '.json': 'application/json',
        '.epub': 'application/epub+zip',
        
        # Archives
        '.zip': 'application/zip',
        
        # Email
        '.msg': 'application/vnd.ms-outlook',
        
        # Audio (with transcription)
        '.wav': 'audio/wav',
        '.mp3': 'audio/mpeg',
        '.m4a': 'audio/mp4',
        
        # Notebooks
        '.ipynb': 'application/json',
        
        # Text formats
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.rtf': 'text/rtf'
    }
    
    @staticmethod
    def init_app(app):
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.CONVERTED_FOLDER, exist_ok=True)

        # Create database directory if using SQLite
        if Config.DATABASE_URL.startswith('sqlite:///'):
            db_path = Config.DATABASE_URL.replace('sqlite:///', '')
            db_dir = os.path.dirname(db_path)
            if db_dir:
                os.makedirs(db_dir, exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
