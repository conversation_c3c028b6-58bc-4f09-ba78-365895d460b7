from flask import Flask, request, jsonify, send_file, current_app
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, jwt_required, create_access_token, get_jwt_identity
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
import os
import uuid
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any

from config import config
from models import db, User, ConversionFile, ShareLink, create_default_admin
from services.conversion_service import ConversionService
from services.file_service import FileService

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])
    jwt = JWTManager(app)
    
    # Initialize configuration
    config[config_name].init_app(app)
    
    # Initialize services
    conversion_service = ConversionService(app)
    file_service = FileService(app)
    
    # Create database tables
    with app.app_context():
        db.create_all()
        create_default_admin(app)
    
    # Error handlers
    @app.errorhandler(RequestEntityTooLarge)
    def handle_file_too_large(e):
        return jsonify({'error': 'File too large. Maximum size is 100MB.'}), 413
    
    @app.errorhandler(404)
    def handle_not_found(e):
        return jsonify({'error': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def handle_internal_error(e):
        return jsonify({'error': 'Internal server error'}), 500
    
    # Authentication routes
    @app.route('/api/auth/login', methods=['POST'])
    def login():
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400
        
        user = User.query.filter_by(username=username).first()
        if not user or not user.check_password(password):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        access_token = create_access_token(identity=user.id)
        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        })
    
    @app.route('/api/auth/register', methods=['POST'])
    def register():
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')
        
        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            return jsonify({'error': 'Username already exists'}), 409
        
        if email and User.query.filter_by(email=email).first():
            return jsonify({'error': 'Email already exists'}), 409
        
        # Create new user
        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        
        access_token = create_access_token(identity=user.id)
        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        }), 201
    
    @app.route('/api/auth/me', methods=['GET'])
    @jwt_required()
    def get_current_user():
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        return jsonify({'user': user.to_dict()})
    
    # File upload route
    @app.route('/api/upload', methods=['POST'])
    @jwt_required()
    def upload_files():
        user_id = get_jwt_identity()
        
        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400
        
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'error': 'No files selected'}), 400
        
        uploaded_files = []
        errors = []
        
        for file in files:
            if file.filename == '':
                continue
                
            try:
                result = file_service.save_uploaded_file(file, user_id)
                uploaded_files.append(result)
            except ValueError as e:
                errors.append({'filename': file.filename, 'error': str(e)})
            except Exception as e:
                errors.append({'filename': file.filename, 'error': 'Upload failed'})
        
        return jsonify({
            'uploaded_files': uploaded_files,
            'errors': errors
        }), 200 if not errors else 207
    
    # File management routes
    @app.route('/api/files', methods=['GET'])
    @jwt_required()
    def get_user_files():
        user_id = get_jwt_identity()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        query = ConversionFile.query.filter_by(user_id=user_id)
        if status:
            query = query.filter_by(status=status)
        
        files = query.order_by(ConversionFile.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'files': [f.to_dict() for f in files.items],
            'total': files.total,
            'pages': files.pages,
            'current_page': page
        })
    
    # Conversion routes
    @app.route('/api/convert', methods=['POST'])
    @jwt_required()
    def start_conversion():
        user_id = get_jwt_identity()
        data = request.get_json()
        file_ids = data.get('file_ids', [])

        if not file_ids:
            return jsonify({'error': 'No files specified for conversion'}), 400

        # Verify files belong to user
        files = ConversionFile.query.filter(
            ConversionFile.file_id.in_(file_ids),
            ConversionFile.user_id == user_id
        ).all()

        if len(files) != len(file_ids):
            return jsonify({'error': 'Some files not found or not accessible'}), 404

        # Start conversion in background
        def convert_files():
            with app.app_context():
                conversion_service.convert_files(files)

        thread = threading.Thread(target=convert_files)
        thread.daemon = True
        thread.start()

        return jsonify({'message': f'Conversion started for {len(files)} files'})

    # Download routes
    @app.route('/api/download/<file_id>', methods=['GET'])
    @jwt_required()
    def download_file(file_id):
        user_id = get_jwt_identity()
        file = file_service.get_file_by_id(file_id, user_id)

        if not file:
            return jsonify({'error': 'File not found'}), 404

        if file.status != 'completed':
            return jsonify({'error': 'File conversion not completed'}), 400

        if not os.path.exists(file.converted_path):
            return jsonify({'error': 'Converted file not found on disk'}), 404

        return send_file(
            file.converted_path,
            as_attachment=True,
            download_name=file.converted_filename,
            mimetype='text/markdown'
        )

    # Share link routes
    @app.route('/api/share/<file_id>', methods=['POST'])
    @jwt_required()
    def create_share_link(file_id):
        user_id = get_jwt_identity()
        data = request.get_json() or {}

        expires_in_days = data.get('expires_in_days')
        max_access = data.get('max_access')

        try:
            share_link = file_service.create_share_link(
                file_id, user_id, expires_in_days, max_access
            )
            return jsonify({
                'share_link': share_link.to_dict(),
                'share_url': f"/api/shared/{share_link.share_id}"
            })
        except ValueError as e:
            return jsonify({'error': str(e)}), 400

    @app.route('/api/shared/<share_id>', methods=['GET'])
    def access_shared_file(share_id):
        file, error = file_service.access_shared_file(share_id)

        if error:
            return jsonify({'error': error}), 404 if 'not found' in error else 403

        if not os.path.exists(file.converted_path):
            return jsonify({'error': 'File not found on disk'}), 404

        return send_file(
            file.converted_path,
            as_attachment=True,
            download_name=file.converted_filename,
            mimetype='text/markdown'
        )

    @app.route('/api/share-links', methods=['GET'])
    @jwt_required()
    def get_user_share_links():
        user_id = get_jwt_identity()
        share_links = file_service.get_user_share_links(user_id)

        return jsonify({
            'share_links': [link.to_dict() for link in share_links]
        })

    @app.route('/api/share/<share_id>/deactivate', methods=['POST'])
    @jwt_required()
    def deactivate_share_link(share_id):
        user_id = get_jwt_identity()

        if file_service.deactivate_share_link(share_id, user_id):
            return jsonify({'message': 'Share link deactivated'})
        else:
            return jsonify({'error': 'Share link not found'}), 404

    # File deletion route
    @app.route('/api/files/<file_id>', methods=['DELETE'])
    @jwt_required()
    def delete_file(file_id):
        user_id = get_jwt_identity()

        if file_service.delete_file(file_id, user_id):
            return jsonify({'message': 'File deleted successfully'})
        else:
            return jsonify({'error': 'File not found'}), 404

    # Supported formats route
    @app.route('/api/supported-formats', methods=['GET'])
    def get_supported_formats():
        return jsonify({
            'formats': conversion_service.get_supported_formats()
        })

    return app

if __name__ == '__main__':
    app = create_app(os.environ.get('FLASK_ENV', 'development'))
    app.run(host='0.0.0.0', port=5000, debug=app.config['DEBUG'])
