from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
import uuid
import hashlib
import bcrypt
from typing import Optional

db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(128), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    files = db.relationship('ConversionFile', backref='user', lazy=True, cascade='all, delete-orphan')
    share_links = db.relationship('ShareLink', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def set_password(self, password: str):
        """Hash and set the user's password."""
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def check_password(self, password: str) -> bool:
        """Check if the provided password matches the user's password."""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class ConversionFile(db.Model):
    __tablename__ = 'conversion_files'
    
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Original file info
    original_filename = db.Column(db.String(255), nullable=False)
    original_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # in bytes
    file_type = db.Column(db.String(100), nullable=False)  # MIME type
    file_extension = db.Column(db.String(10), nullable=False)
    
    # Conversion info
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, processing, completed, failed
    converted_filename = db.Column(db.String(255), nullable=True)
    converted_path = db.Column(db.String(500), nullable=True)
    converted_size = db.Column(db.Integer, nullable=True)
    
    # Metadata
    conversion_started_at = db.Column(db.DateTime, nullable=True)
    conversion_completed_at = db.Column(db.DateTime, nullable=True)
    error_message = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    share_links = db.relationship('ShareLink', backref='file', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_id': self.file_id,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'status': self.status,
            'converted_filename': self.converted_filename,
            'converted_size': self.converted_size,
            'conversion_started_at': self.conversion_started_at.isoformat() if self.conversion_started_at else None,
            'conversion_completed_at': self.conversion_completed_at.isoformat() if self.conversion_completed_at else None,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'has_share_links': len(self.share_links) > 0
        }

class ShareLink(db.Model):
    __tablename__ = 'share_links'
    
    id = db.Column(db.Integer, primary_key=True)
    share_id = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    file_id = db.Column(db.Integer, db.ForeignKey('conversion_files.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Share settings
    expires_at = db.Column(db.DateTime, nullable=True)  # None means never expires
    access_count = db.Column(db.Integer, default=0)
    max_access_count = db.Column(db.Integer, nullable=True)  # None means unlimited
    is_active = db.Column(db.Boolean, default=True)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_accessed_at = db.Column(db.DateTime, nullable=True)
    
    def is_valid(self) -> bool:
        """Check if the share link is still valid."""
        if not self.is_active:
            return False
        
        # Check expiration
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False
        
        # Check access count limit
        if self.max_access_count and self.access_count >= self.max_access_count:
            return False
        
        return True
    
    def increment_access(self):
        """Increment the access count and update last accessed time."""
        self.access_count += 1
        self.last_accessed_at = datetime.utcnow()
    
    def to_dict(self):
        return {
            'id': self.id,
            'share_id': self.share_id,
            'file_id': self.file_id,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'access_count': self.access_count,
            'max_access_count': self.max_access_count,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_accessed_at': self.last_accessed_at.isoformat() if self.last_accessed_at else None,
            'is_valid': self.is_valid()
        }

def create_default_admin(app):
    """Create the default admin user if it doesn't exist."""
    with app.app_context():
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            admin_user.set_password('rwz.dpj3RDW3gwz5fny')
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin / rwz.dpj3RDW3gwz5fny")
