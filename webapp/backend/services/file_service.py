import os
import uuid
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
from flask import current_app

from models import db, ConversionFile, ShareLink

class FileService:
    def __init__(self, app):
        self.app = app
    
    def save_uploaded_file(self, file, user_id: int) -> dict:
        """Save an uploaded file and create a database record."""
        if not file or file.filename == '':
            raise ValueError("No file provided")
        
        # Check file extension
        filename = secure_filename(file.filename)
        if not filename:
            raise ValueError("Invalid filename")
        
        _, ext = os.path.splitext(filename.lower())
        if ext not in self.app.config['SUPPORTED_EXTENSIONS']:
            raise ValueError(f"Unsupported file format: {ext}")
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        unique_filename = f"{file_id}_{filename}"
        file_path = os.path.join(self.app.config['UPLOAD_FOLDER'], unique_filename)
        
        # Save the file
        file.save(file_path)
        file_size = os.path.getsize(file_path)
        
        # Create database record
        conversion_file = ConversionFile(
            file_id=file_id,
            user_id=user_id,
            original_filename=filename,
            original_path=file_path,
            file_size=file_size,
            file_type=self.app.config['SUPPORTED_EXTENSIONS'][ext],
            file_extension=ext,
            status='pending'
        )
        
        db.session.add(conversion_file)
        db.session.commit()
        
        return conversion_file.to_dict()
    
    def get_file_by_id(self, file_id: str, user_id: int = None) -> ConversionFile:
        """Get a file by its ID, optionally filtered by user."""
        query = ConversionFile.query.filter_by(file_id=file_id)
        if user_id:
            query = query.filter_by(user_id=user_id)
        return query.first()
    
    def delete_file(self, file_id: str, user_id: int) -> bool:
        """Delete a file and its associated records."""
        file = self.get_file_by_id(file_id, user_id)
        if not file:
            return False
        
        # Delete physical files
        try:
            if os.path.exists(file.original_path):
                os.remove(file.original_path)
            if file.converted_path and os.path.exists(file.converted_path):
                os.remove(file.converted_path)
        except OSError:
            pass  # File might already be deleted
        
        # Delete database record (cascade will handle share links)
        db.session.delete(file)
        db.session.commit()
        
        return True
    
    def create_share_link(self, file_id: str, user_id: int, expires_in_days: int = None, max_access: int = None) -> ShareLink:
        """Create a share link for a file."""
        file = self.get_file_by_id(file_id, user_id)
        if not file:
            raise ValueError("File not found")
        
        if file.status != 'completed':
            raise ValueError("File must be successfully converted before sharing")
        
        # Calculate expiration date
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # Create share link
        share_link = ShareLink(
            file_id=file.id,
            user_id=user_id,
            expires_at=expires_at,
            max_access_count=max_access
        )
        
        db.session.add(share_link)
        db.session.commit()
        
        return share_link
    
    def get_share_link(self, share_id: str) -> ShareLink:
        """Get a share link by its ID."""
        return ShareLink.query.filter_by(share_id=share_id).first()
    
    def access_shared_file(self, share_id: str) -> tuple:
        """Access a shared file and return the file path and info."""
        share_link = self.get_share_link(share_id)
        if not share_link:
            return None, "Share link not found"
        
        if not share_link.is_valid():
            return None, "Share link has expired or is no longer valid"
        
        # Increment access count
        share_link.increment_access()
        db.session.commit()
        
        return share_link.file, None
    
    def get_user_share_links(self, user_id: int) -> list:
        """Get all share links created by a user."""
        return ShareLink.query.filter_by(user_id=user_id).order_by(ShareLink.created_at.desc()).all()
    
    def deactivate_share_link(self, share_id: str, user_id: int) -> bool:
        """Deactivate a share link."""
        share_link = ShareLink.query.filter_by(share_id=share_id, user_id=user_id).first()
        if not share_link:
            return False
        
        share_link.is_active = False
        db.session.commit()
        return True
    
    def cleanup_expired_files(self, days_old: int = 30):
        """Clean up old files and expired share links."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        # Find old files
        old_files = ConversionFile.query.filter(
            ConversionFile.created_at < cutoff_date
        ).all()
        
        for file in old_files:
            try:
                # Delete physical files
                if os.path.exists(file.original_path):
                    os.remove(file.original_path)
                if file.converted_path and os.path.exists(file.converted_path):
                    os.remove(file.converted_path)
                
                # Delete database record
                db.session.delete(file)
            except Exception as e:
                print(f"Error cleaning up file {file.file_id}: {str(e)}")
        
        # Deactivate expired share links
        expired_links = ShareLink.query.filter(
            ShareLink.expires_at < datetime.utcnow(),
            ShareLink.is_active == True
        ).all()
        
        for link in expired_links:
            link.is_active = False
        
        db.session.commit()
        
        return len(old_files), len(expired_links)
