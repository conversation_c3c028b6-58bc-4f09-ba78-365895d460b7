import os
import traceback
from datetime import datetime
from typing import List
from markitdown import MarkItDown, UnsupportedFormatException, FileConversionException

from models import db, ConversionFile

class ConversionService:
    def __init__(self, app):
        self.app = app
        self.markitdown = MarkItDown()
        
    def convert_files(self, files: List[ConversionFile]):
        """Convert a list of files to Markdown."""
        for file in files:
            self.convert_single_file(file)
    
    def convert_single_file(self, file: ConversionFile):
        """Convert a single file to Markdown."""
        try:
            # Update status to processing
            file.status = 'processing'
            file.conversion_started_at = datetime.utcnow()
            db.session.commit()
            
            # Check if original file exists
            if not os.path.exists(file.original_path):
                raise FileNotFoundError(f"Original file not found: {file.original_path}")
            
            # Convert the file
            result = self.markitdown.convert(file.original_path)
            
            # Generate output filename and path
            base_name = os.path.splitext(file.original_filename)[0]
            converted_filename = f"{base_name}.md"
            
            # Create date-based subdirectory
            date_dir = datetime.utcnow().strftime('%Y-%m-%d')
            output_dir = os.path.join(self.app.config['CONVERTED_FOLDER'], date_dir)
            os.makedirs(output_dir, exist_ok=True)
            
            # Ensure unique filename
            counter = 1
            converted_path = os.path.join(output_dir, converted_filename)
            while os.path.exists(converted_path):
                name, ext = os.path.splitext(converted_filename)
                converted_filename = f"{name}_{counter}{ext}"
                converted_path = os.path.join(output_dir, converted_filename)
                counter += 1
            
            # Write the converted content
            with open(converted_path, 'w', encoding='utf-8') as f:
                f.write(result.markdown)
            
            # Update file record
            file.status = 'completed'
            file.converted_filename = converted_filename
            file.converted_path = converted_path
            file.converted_size = os.path.getsize(converted_path)
            file.conversion_completed_at = datetime.utcnow()
            file.error_message = None
            
            db.session.commit()
            
            print(f"Successfully converted {file.original_filename} to {converted_filename}")
            
        except UnsupportedFormatException as e:
            self._handle_conversion_error(file, f"Unsupported file format: {str(e)}")
        except FileConversionException as e:
            self._handle_conversion_error(file, f"Conversion failed: {str(e)}")
        except FileNotFoundError as e:
            self._handle_conversion_error(file, str(e))
        except Exception as e:
            self._handle_conversion_error(file, f"Unexpected error: {str(e)}")
    
    def _handle_conversion_error(self, file: ConversionFile, error_message: str):
        """Handle conversion errors by updating the file status."""
        try:
            file.status = 'failed'
            file.error_message = error_message
            file.conversion_completed_at = datetime.utcnow()
            db.session.commit()
            
            print(f"Conversion failed for {file.original_filename}: {error_message}")
            
        except Exception as e:
            print(f"Error updating file status: {str(e)}")
            traceback.print_exc()
    
    def get_supported_formats(self) -> dict:
        """Get the list of supported file formats."""
        return self.app.config['SUPPORTED_EXTENSIONS']
    
    def is_supported_format(self, filename: str) -> bool:
        """Check if a file format is supported."""
        _, ext = os.path.splitext(filename.lower())
        return ext in self.app.config['SUPPORTED_EXTENSIONS']
    
    def get_file_type(self, filename: str) -> str:
        """Get the MIME type for a file based on its extension."""
        _, ext = os.path.splitext(filename.lower())
        return self.app.config['SUPPORTED_EXTENSIONS'].get(ext, 'application/octet-stream')
