version: '3.8'

services:
  markitdown:
    build: .
    image: vietprogrammer/markitdown:latest
    container_name: markitdown-webapp
    ports:
      - "8080:8080"
    volumes:
      # Persistent storage for database and files
      - markitdown_data:/app/data
    environment:
      # Database configuration
      - DATABASE_URL=sqlite:///data/markitdown.db
      - UPLOAD_FOLDER=data/uploads
      - CONVERTED_FOLDER=data/converted
      
      # Security configuration
      - SECRET_KEY=your-secret-key-change-in-production
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      
      # Admin configuration (optional override)
      # - ADMIN_PASSWORD=your-custom-admin-password
      
      # CORS configuration (optional)
      # - CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
      
      # Flask environment
      - FLASK_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/supported-formats"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "com.docker.compose.project=markitdown"
      - "com.docker.compose.service=webapp"

volumes:
  markitdown_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data

networks:
  default:
    name: markitdown-network
